// 🕵️ SPY KID BOOKMARKLET VERSION
// Save this as a bookmark and click it on the site

javascript:(function(){
    console.log('🕵️‍♂️ SPY KID BOOKMARKLET ACTIVATED!');
    
    const config = {
        target: 'female',
        currency: 99999,
        fakeUserId: 'spykid_bm_' + Math.random().toString(36).substr(2, 9)
    };
    
    // Account spoofing
    const userData = {
        id: config.fakeUserId,
        premium: true,
        balance: config.currency,
        gems: config.currency,
        preferences: { gender: config.target }
    };
    
    ['user', 'userData', 'coomeet-user', 'ftf-user'].forEach(key => {
        localStorage.setItem(key, JSON.stringify(userData));
    });
    
    ['balance', 'gems', 'credits', 'coomeet-balance', 'ftf-balance'].forEach(key => {
        localStorage.setItem(key, config.currency.toString());
    });
    
    ['premium', 'vip', 'isLoggedIn', 'authenticated'].forEach(key => {
        localStorage.setItem(key, 'true');
    });
    
    ['gender', 'coomeet-app-version', 'preferred-gender'].forEach(key => {
        localStorage.setItem(key, config.target);
    });
    
    // Auth bypass
    window.isLoggedIn = () => true;
    window.isPremium = () => true;
    window.getBalance = () => config.currency;
    
    // Media bypass
    const fakeStream = {
        getTracks: () => [
            { kind: 'video', enabled: true, readyState: 'live', stop: () => {} },
            { kind: 'audio', enabled: true, readyState: 'live', stop: () => {} }
        ],
        active: true
    };
    
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia = () => Promise.resolve(fakeStream);
    }
    
    if (navigator.permissions && navigator.permissions.query) {
        const orig = navigator.permissions.query;
        navigator.permissions.query = function(desc) {
            if (desc.name === 'camera' || desc.name === 'microphone') {
                return Promise.resolve({ state: 'granted' });
            }
            return orig.apply(this, arguments);
        };
    }
    
    // Iframe communication
    const iframe = document.querySelector('iframe');
    if (iframe && iframe.contentWindow) {
        iframe.contentWindow.postMessage({
            type: 'COMPLETE_BYPASS',
            gender: config.target,
            premium: true,
            balance: config.currency,
            cameraGranted: true,
            authenticated: true
        }, '*');
    }
    
    // Visual indicator
    const indicator = document.createElement('div');
    indicator.innerHTML = '🕵️‍♂️ SPY KID ACTIVE<br>💰 99999 GEMS<br>🎯 FEMALE<br>🎥 BYPASSED';
    indicator.style.cssText = 'position:fixed;top:10px;right:10px;background:linear-gradient(45deg,#ff6b6b,#4ecdc4);color:white;padding:10px;border-radius:5px;font-weight:bold;z-index:999999;font-family:monospace;font-size:10px;text-align:center;border:2px solid white;';
    document.body.appendChild(indicator);
    
    console.log('✅ SPY KID BOOKMARKLET COMPLETE!');
    alert('🎯 SPY KID HACK ACTIVE!\n💰 Premium: 99999 gems\n🎯 Gender: Female\n🎥 Camera/Mic: Bypassed\n🚀 Ready to START!');
})();
