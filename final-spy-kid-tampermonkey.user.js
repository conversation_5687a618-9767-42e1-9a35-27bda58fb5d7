// ==UserScript==
// @name         🕵️ SPY KID ULTIMATE: Complete Bypass Suite
// @namespace    http://tampermonkey.net/
// @version      4.0
// @description  🎯 Ultimate hack: Account bypass, female selection, camera/mic bypass, unlimited currency
// <AUTHOR> Kid Agent
// @match        https://www.omegla.live/dirtyroulette/*
// @match        https://omegla.live/dirtyroulette/*
// @match        https://ftf.live/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🕵️‍♂️ SPY KID ULTIMATE HACK: ACCOUNT BYPASS + FEMALE SELECTION ACTIVATED!');
    
    // 🎯 MISSION CONFIGURATION
    const MISSION_CONFIG = {
        target: 'female',
        fakeUserId: 'spykid_' + Math.random().toString(36).substr(2, 9),
        currency: 99999,
        gems: 99999,
        credits: 99999,
        premium: true,
        vip: true,
        debug: true
    };
    
    // 💰 COMPLETE ACCOUNT SPOOFING
    function executeAccountSpoof() {
        if (MISSION_CONFIG.debug) console.log('💰 Executing complete account spoofing...');
        
        try {
            // Generate comprehensive fake user data
            const fakeUserData = {
                id: MISSION_CONFIG.fakeUserId,
                username: 'SpyKidPremium',
                email: '<EMAIL>',
                premium: true,
                vip: true,
                verified: true,
                balance: MISSION_CONFIG.currency,
                gems: MISSION_CONFIG.gems,
                credits: MISSION_CONFIG.credits,
                tokens: MISSION_CONFIG.currency,
                coins: MISSION_CONFIG.currency,
                subscription: 'premium_lifetime',
                subscriptionExpiry: Date.now() + (10 * 365 * 24 * 60 * 60 * 1000), // 10 years
                accountCreated: Date.now() - (365 * 24 * 60 * 60 * 1000), // 1 year ago
                lastLogin: Date.now(),
                preferences: {
                    gender: MISSION_CONFIG.target,
                    targetGender: MISSION_CONFIG.target,
                    genderPreference: MISSION_CONFIG.target,
                    autoSelectFemale: true
                },
                settings: {
                    GenderPreference: MISSION_CONFIG.target,
                    TargetGender: MISSION_CONFIG.target,
                    MatchGender: MISSION_CONFIG.target,
                    PreferredGender: MISSION_CONFIG.target
                }
            };
            
            // Inject into all possible storage locations
            const storageKeys = [
                'user', 'userData', 'userInfo', 'currentUser', 'loggedUser', 'activeUser',
                'coomeet-user', 'ftf-user', 'chat-user', 'app-user', 'site-user',
                'coomeet-user-data', 'ftf-user-data', 'user-profile', 'user-account',
                'account', 'accountData', 'profile', 'userAccount', 'memberData'
            ];
            
            storageKeys.forEach(key => {
                localStorage.setItem(key, JSON.stringify(fakeUserData));
                sessionStorage.setItem(key, JSON.stringify(fakeUserData));
            });
            
            // Generate and inject authentication tokens
            const tokenTypes = ['access', 'refresh', 'session', 'auth', 'bearer', 'jwt'];
            tokenTypes.forEach(type => {
                const token = `spykid_${type}_` + Math.random().toString(36).substr(2, 25);
                localStorage.setItem(`${type}Token`, token);
                localStorage.setItem(`coomeet-${type}-token`, token);
                localStorage.setItem(`ftf-${type}-token`, token);
                sessionStorage.setItem(`${type}Token`, token);
            });
            
            // Set currency in all possible locations
            const currencyKeys = [
                'balance', 'gems', 'credits', 'tokens', 'coins', 'currency', 'money',
                'coomeet-balance', 'coomeet-gems', 'coomeet-credits', 'coomeet-tokens',
                'ftf-balance', 'ftf-gems', 'ftf-credits', 'ftf-tokens',
                'user-balance', 'user-gems', 'user-credits', 'wallet'
            ];
            
            currencyKeys.forEach(key => {
                localStorage.setItem(key, MISSION_CONFIG.currency.toString());
                sessionStorage.setItem(key, MISSION_CONFIG.currency.toString());
            });
            
            // Set premium/VIP status flags
            const premiumKeys = [
                'premium', 'vip', 'isPremium', 'isVip', 'hasSubscription', 'subscribed',
                'coomeet-premium', 'ftf-premium', 'user-premium', 'premium-user',
                'subscription', 'subscriptionActive', 'membershipActive', 'vipMember'
            ];
            
            premiumKeys.forEach(key => {
                localStorage.setItem(key, 'true');
                sessionStorage.setItem(key, 'true');
            });
            
            // Set authentication status
            const authKeys = [
                'isLoggedIn', 'authenticated', 'loggedIn', 'userLoggedIn', 'authStatus',
                'coomeet-logged-in', 'ftf-logged-in', 'auth-status', 'login-status',
                'session-active', 'user-authenticated', 'loginComplete'
            ];
            
            authKeys.forEach(key => {
                localStorage.setItem(key, 'true');
                sessionStorage.setItem(key, 'true');
            });
            
            if (MISSION_CONFIG.debug) console.log('✅ Complete account spoofing successful');
            return true;
        } catch (error) {
            console.log('❌ Account spoofing failed:', error.message);
            return false;
        }
    }
    
    // 🔓 ADVANCED AUTHENTICATION BYPASS
    function executeAuthBypass() {
        if (MISSION_CONFIG.debug) console.log('🔓 Executing advanced authentication bypass...');
        
        try {
            // Override all authentication-related functions
            const authFunctions = {
                isAuthenticated: () => true,
                isLoggedIn: () => true,
                hasAccount: () => true,
                isPremium: () => true,
                isVip: () => true,
                hasCredits: () => true,
                hasSubscription: () => true,
                getBalance: () => MISSION_CONFIG.currency,
                getGems: () => MISSION_CONFIG.gems,
                getCredits: () => MISSION_CONFIG.credits,
                getUserId: () => MISSION_CONFIG.fakeUserId,
                checkAuth: () => true,
                requireAuth: () => true,
                validateUser: () => true
            };
            
            Object.entries(authFunctions).forEach(([name, func]) => {
                window[name] = func;
                if (window.parent) window.parent[name] = func;
            });
            
            // Intercept and spoof network requests
            const originalFetch = window.fetch;
            window.fetch = function(url, options) {
                const urlStr = url.toString();
                if (urlStr.includes('login') || urlStr.includes('auth') || urlStr.includes('verify') || 
                    urlStr.includes('balance') || urlStr.includes('user') || urlStr.includes('premium')) {
                    
                    if (MISSION_CONFIG.debug) console.log('🚫 Network request intercepted:', urlStr);
                    
                    return Promise.resolve({
                        ok: true,
                        status: 200,
                        json: () => Promise.resolve({
                            success: true,
                            authenticated: true,
                            premium: true,
                            vip: true,
                            user: {
                                id: MISSION_CONFIG.fakeUserId,
                                premium: true,
                                balance: MISSION_CONFIG.currency,
                                gems: MISSION_CONFIG.gems,
                                credits: MISSION_CONFIG.credits,
                                subscription: 'premium'
                            },
                            balance: MISSION_CONFIG.currency,
                            gems: MISSION_CONFIG.gems,
                            credits: MISSION_CONFIG.credits
                        }),
                        text: () => Promise.resolve(JSON.stringify({
                            success: true,
                            premium: true,
                            balance: MISSION_CONFIG.currency
                        }))
                    });
                }
                return originalFetch.apply(this, arguments);
            };
            
            if (MISSION_CONFIG.debug) console.log('✅ Advanced authentication bypass complete');
            return true;
        } catch (error) {
            console.log('❌ Authentication bypass failed:', error.message);
            return false;
        }
    }
    
    // 🎥 CAMERA/MIC PERMISSION BYPASS
    function executeMediaPermissionBypass() {
        if (MISSION_CONFIG.debug) console.log('🎥 Executing camera/mic permission bypass...');

        try {
            // Create fake media stream
            const fakeStream = {
                getTracks: () => [
                    {
                        kind: 'video',
                        enabled: true,
                        readyState: 'live',
                        stop: () => {},
                        addEventListener: () => {},
                        removeEventListener: () => {}
                    },
                    {
                        kind: 'audio',
                        enabled: true,
                        readyState: 'live',
                        stop: () => {},
                        addEventListener: () => {},
                        removeEventListener: () => {}
                    }
                ],
                getVideoTracks: () => [{
                    kind: 'video',
                    enabled: true,
                    readyState: 'live',
                    stop: () => {}
                }],
                getAudioTracks: () => [{
                    kind: 'audio',
                    enabled: true,
                    readyState: 'live',
                    stop: () => {}
                }],
                active: true,
                id: 'fake-stream-' + Math.random().toString(36).substr(2, 9)
            };

            // Override all getUserMedia variants
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                navigator.mediaDevices.getUserMedia = function(constraints) {
                    if (MISSION_CONFIG.debug) console.log('🚫 getUserMedia intercepted, returning fake stream');
                    return Promise.resolve(fakeStream);
                };
            }

            if (navigator.getUserMedia) {
                navigator.getUserMedia = function(constraints, success, error) {
                    if (MISSION_CONFIG.debug) console.log('🚫 Legacy getUserMedia intercepted');
                    if (success) success(fakeStream);
                };
            }

            if (navigator.webkitGetUserMedia) {
                navigator.webkitGetUserMedia = function(constraints, success, error) {
                    if (MISSION_CONFIG.debug) console.log('🚫 WebKit getUserMedia intercepted');
                    if (success) success(fakeStream);
                };
            }

            if (navigator.mozGetUserMedia) {
                navigator.mozGetUserMedia = function(constraints, success, error) {
                    if (MISSION_CONFIG.debug) console.log('🚫 Mozilla getUserMedia intercepted');
                    if (success) success(fakeStream);
                };
            }

            // Override permission API
            if (navigator.permissions && navigator.permissions.query) {
                const originalQuery = navigator.permissions.query;
                navigator.permissions.query = function(permissionDesc) {
                    if (permissionDesc.name === 'camera' || permissionDesc.name === 'microphone') {
                        if (MISSION_CONFIG.debug) console.log('🚫 Permission query intercepted for:', permissionDesc.name);
                        return Promise.resolve({ state: 'granted' });
                    }
                    return originalQuery.apply(this, arguments);
                };
            }

            // Set permission flags in storage
            const permissionKeys = [
                'camera-permission', 'microphone-permission', 'media-permissions',
                'coomeet-camera-permission', 'ftf-camera-permission',
                'coomeet-microphone-permission', 'ftf-microphone-permission',
                'media-access-granted', 'camera-access', 'mic-access'
            ];

            permissionKeys.forEach(key => {
                localStorage.setItem(key, 'granted');
                sessionStorage.setItem(key, 'granted');
            });

            if (MISSION_CONFIG.debug) console.log('✅ Camera/Mic permission bypass complete');
            return true;
        } catch (error) {
            console.log('❌ Media permission bypass failed:', error.message);
            return false;
        }
    }

    // 🎯 GENDER SELECTION + IFRAME COMMUNICATION
    function executeGenderHack() {
        if (MISSION_CONFIG.debug) console.log('🎯 Executing gender selection hack...');

        try {
            // Set gender preferences in all storage locations
            const genderKeys = [
                'gender', 'preferred-gender', 'match-gender', 'target-gender',
                'coomeet-gender', 'ftf-gender', 'user-preference', 'chat-gender',
                'coomeet-app-version', 'gender-preference', 'selected-gender'
            ];

            genderKeys.forEach(key => {
                localStorage.setItem(key, MISSION_CONFIG.target);
                sessionStorage.setItem(key, MISSION_CONFIG.target);
            });

            // Modify cache data
            const cacheKey = 'coomeet-app-cache-o';
            const cacheData = localStorage.getItem(cacheKey);
            if (cacheData) {
                try {
                    const cache = JSON.parse(cacheData);
                    if (cache.user && cache.user.settings) {
                        cache.user.settings.GenderPreference = MISSION_CONFIG.target;
                        cache.user.settings.TargetGender = MISSION_CONFIG.target;
                        cache.user.settings.MatchGender = MISSION_CONFIG.target;
                    }
                    localStorage.setItem(cacheKey, JSON.stringify(cache));
                } catch (e) {
                    if (MISSION_CONFIG.debug) console.log('Cache modification failed:', e.message);
                }
            }

            // Send messages to iframe
            const sendIframeMessages = () => {
                const iframe = document.querySelector('iframe');
                if (iframe && iframe.contentWindow) {
                    const messages = [
                        {
                            type: 'FORCE_GENDER_SELECTION',
                            gender: MISSION_CONFIG.target,
                            bypass: true,
                            premium: true,
                            balance: MISSION_CONFIG.currency
                        },
                        {
                            type: 'CURRENCY_INJECTION',
                            balance: MISSION_CONFIG.currency,
                            gems: MISSION_CONFIG.gems,
                            credits: MISSION_CONFIG.credits,
                            premium: true,
                            vip: true,
                            unlimited: true
                        },
                        {
                            type: 'AUTH_BYPASS',
                            authenticated: true,
                            userId: MISSION_CONFIG.fakeUserId,
                            premium: true,
                            skipLogin: true
                        },
                        {
                            type: 'MEDIA_PERMISSION_BYPASS',
                            cameraGranted: true,
                            microphoneGranted: true,
                            mediaAccess: true,
                            skipPermissions: true
                        }
                    ];

                    messages.forEach(message => {
                        iframe.contentWindow.postMessage(message, '*');
                    });

                    if (MISSION_CONFIG.debug) console.log('✅ Messages sent to iframe');
                }
            };

            // Send messages immediately and set up interval
            sendIframeMessages();
            setInterval(sendIframeMessages, 3000);

            if (MISSION_CONFIG.debug) console.log('✅ Gender selection hack complete');
            return true;
        } catch (error) {
            console.log('❌ Gender hack failed:', error.message);
            return false;
        }
    }
    
    // 🚀 EXECUTE COMPLETE MISSION
    function executeCompleteMission() {
        if (MISSION_CONFIG.debug) console.log('🚀 Executing complete spy kid mission...');

        // Execute all attack vectors
        const results = {
            account: executeAccountSpoof(),
            auth: executeAuthBypass(),
            media: executeMediaPermissionBypass(),
            gender: executeGenderHack()
        };
        
        // Set up continuous monitoring and maintenance
        setInterval(() => {
            // Maintain account status
            localStorage.setItem('isLoggedIn', 'true');
            localStorage.setItem('premium', 'true');
            localStorage.setItem('balance', MISSION_CONFIG.currency.toString());
            localStorage.setItem('gems', MISSION_CONFIG.gems.toString());
            localStorage.setItem('coomeet-app-version', MISSION_CONFIG.target);
            
            // Ensure authentication functions remain overridden
            window.isLoggedIn = () => true;
            window.isPremium = () => true;
            window.getBalance = () => MISSION_CONFIG.currency;
        }, 2000);
        
        if (MISSION_CONFIG.debug) {
            console.log('🎯 COMPLETE SPY KID MISSION ACCOMPLISHED!');
            console.log('💰 Status: Premium User with', MISSION_CONFIG.currency, 'currency');
            console.log('🎯 Gender: Female selection active');
            console.log('🔓 Authentication: Completely bypassed');
            console.log('📊 Results:', results);
        }
        
        return results;
    }
    
    // 🎬 INITIALIZE MISSION
    const initMission = () => {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', executeCompleteMission);
        } else {
            setTimeout(executeCompleteMission, 500);
        }
    };
    
    // Add visual status indicator
    const addStatusIndicator = () => {
        const indicator = document.createElement('div');
        indicator.innerHTML = `
            🕵️‍♂️ SPY KID ULTIMATE ACTIVE<br>
            💰 ${MISSION_CONFIG.currency} GEMS<br>
            🎯 FEMALE LOCKED<br>
            🔓 PREMIUM BYPASSED<br>
            🎥 CAM/MIC BYPASSED
        `;
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-weight: bold;
            z-index: 999999;
            font-family: 'Courier New', monospace;
            font-size: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            animation: glow 2s ease-in-out infinite alternate;
            text-align: center;
            border: 2px solid #fff;
            backdrop-filter: blur(5px);
        `;
        
        // Add glowing animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes glow {
                from { box-shadow: 0 4px 15px rgba(0,0,0,0.3), 0 0 5px rgba(255,255,255,0.5); }
                to { box-shadow: 0 4px 20px rgba(0,0,0,0.4), 0 0 20px rgba(255,255,255,0.8); }
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(indicator);
    };
    
    // Start mission
    initMission();
    
    // Add indicator when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', addStatusIndicator);
    } else {
        addStatusIndicator();
    }
    
    console.log('🎯 SPY KID ULTIMATE HACK DEPLOYED AND READY!');
    
})();
